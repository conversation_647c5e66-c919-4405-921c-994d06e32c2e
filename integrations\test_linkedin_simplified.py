#!/usr/bin/env python3
"""
Test the simplified LinkedIn integration focused on professional messaging
"""

import requests
import j<PERSON>

def test_simplified_linkedin_page():
    """Test the simplified LinkedIn page"""
    print("📄 Testing Simplified LinkedIn Page")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:8000/linkedin-simple")
        
        if response.status_code == 200:
            print("✅ Simplified LinkedIn page accessible")
            
            content = response.text
            
            # Check for core features
            features = [
                ("Connection Requests", "Connection Requests with Messages"),
                ("InMail", "InMail Messaging"),
                ("Company Messaging", "Company Page Messaging"),
                ("Unipile Integration", "Connect via Unipile"),
                ("API Key", "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk=")
            ]
            
            for feature_name, search_text in features:
                if search_text in content:
                    print(f"✅ {feature_name} feature present")
                else:
                    print(f"❌ {feature_name} feature missing")
            
            return True
        else:
            print(f"❌ Page failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_linkedin_api_endpoints():
    """Test LinkedIn API endpoints for professional messaging"""
    print("\n🌐 Testing LinkedIn Professional Messaging Endpoints")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        ("/api/linkedin/unipile/status", "GET", "Unipile Status"),
        ("/api/linkedin/unipile/connect", "POST", "Unipile Connect"),
        ("/api/linkedin/send-inmail", "POST", "InMail Sending"),
        ("/api/linkedin/send-connection-message", "POST", "Connection Requests"),
        ("/api/linkedin/send-company-message", "POST", "Company Messaging")
    ]
    
    results = []
    
    for endpoint, method, name in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}")
            else:
                # Send test data (will likely fail but should return proper error)
                test_data = {
                    "recipient_id": "test",
                    "message": "test",
                    "subject": "test",
                    "company_id": "test",
                    "api_key": "test"
                }
                response = requests.post(f"{base_url}{endpoint}", json=test_data)
            
            # Check if endpoint exists (not 404)
            if response.status_code != 404:
                print(f"✅ {name} endpoint available ({response.status_code})")
                results.append(True)
            else:
                print(f"❌ {name} endpoint not found")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {name} endpoint error: {e}")
            results.append(False)
    
    return all(results)

def test_unipile_connection():
    """Test Unipile connection for LinkedIn"""
    print("\n🔗 Testing Unipile Connection")
    print("=" * 30)
    
    try:
        # Test status endpoint
        response = requests.get("http://localhost:8000/api/linkedin/unipile/status")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Unipile status endpoint working")
            print(f"   Success: {data.get('success', False)}")
            print(f"   LinkedIn accounts: {data.get('linkedin_accounts', 0)}")
            
            if data.get('linkedin_accounts', 0) > 0:
                print("🎉 LinkedIn accounts found!")
                accounts = data.get('accounts', [])
                for account in accounts:
                    print(f"   - {account.get('name', 'N/A')} ({account.get('id')})")
            else:
                print("⚠️  No LinkedIn accounts in Unipile dashboard")
            
            return True
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Connection test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Simplified LinkedIn Professional Messaging")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("Simplified Page", test_simplified_linkedin_page),
        ("API Endpoints", test_linkedin_api_endpoints),
        ("Unipile Connection", test_unipile_connection)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Simplified LinkedIn integration is working!")
        print("\n💼 Professional Messaging Features Available:")
        print("   • 🤝 Connection requests with personalized messages")
        print("   • 📧 InMail for reaching prospects outside your network")
        print("   • 🏢 Company page messaging for business communications")
        print("\n🔗 Access the interface:")
        print("   http://localhost:8000/linkedin-simple")
    else:
        print("\n⚠️  Some tests failed. Please check the server logs.")
    
    print("\n💡 Next steps:")
    print("   1. Add LinkedIn accounts to your Unipile dashboard")
    print("   2. Visit: https://dashboard.unipile.com")
    print("   3. Click 'Add Account' → 'LinkedIn'")
    print("   4. Test the professional messaging features")
    
    print("\n🔗 Unipile Dashboard: https://dashboard.unipile.com")
