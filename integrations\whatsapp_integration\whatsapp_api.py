"""
WhatsApp Messaging API Integration
Handles authentication and messaging for WhatsApp accounts
Uses Unipile API for unified WhatsApp messaging with QR code authentication
"""

import json
import time
import qrcode
import io
import base64
from typing import Dict, List
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from unipile_config import get_unipile_client

class WhatsAppMessaging:
    def __init__(self, config_path: str = "integrations/whatsapp_integration/config.json"):
        """Initialize WhatsApp messaging client with Unipile API"""
        self.config_path = config_path
        self.config = self._load_config()

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Unipile API setup (only method)
        self.unipile_client = None
        try:
            self.unipile_client = get_unipile_client()
            self.logger.info("Unipile API client initialized for WhatsApp")
        except Exception as e:
            self.logger.error(f"Failed to initialize Unipile API: {e}")
            raise Exception("Unipile API is required for WhatsApp integration")

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 1 second between requests

        # Authentication status
        self.connection_status = {
            "connected": False,
            "qr_code": None,
            "last_check": None,
            "accounts": []
        }
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in config file: {e}")
            return {}
    
    def _save_config(self):
        """Save current configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            self.logger.info("WhatsApp configuration saved")
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    def authenticate_account(self) -> Dict:
        """
        Authenticate WhatsApp account via Unipile
        Generates QR code for WhatsApp Web authentication
        """
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Check if account is already connected
            accounts = self.unipile_client.get_accounts()
            whatsapp_accounts = [acc for acc in accounts.get("items", [])
                               if acc.get("type", "").upper() == "WHATSAPP"]

            if whatsapp_accounts:
                self.connection_status["connected"] = True
                self.connection_status["accounts"] = whatsapp_accounts
                self.connection_status["last_check"] = datetime.now().isoformat()
                self.logger.info("WhatsApp account already connected via Unipile")
                return {
                    "success": True,
                    "message": "WhatsApp account connected",
                    "accounts": whatsapp_accounts,
                    "qr_required": False
                }

            # Generate QR code for authentication
            qr_response = self.unipile_client.generate_whatsapp_qr()
            if "qr_code" in qr_response:
                # Generate QR code image
                qr_code_data = qr_response["qr_code"]
                qr_img = qrcode.make(qr_code_data)

                # Convert to base64 for HTML display
                img_buffer = io.BytesIO()
                qr_img.save(img_buffer, format='PNG')
                img_buffer.seek(0)
                qr_base64 = base64.b64encode(img_buffer.getvalue()).decode()

                self.connection_status["qr_code"] = f"data:image/png;base64,{qr_base64}"
                self.connection_status["last_check"] = datetime.now().isoformat()

                return {
                    "success": True,
                    "message": "QR code generated. Please scan with WhatsApp mobile app.",
                    "qr_code": self.connection_status["qr_code"],
                    "qr_data": qr_code_data,
                    "qr_required": True
                }
            else:
                return {"error": "Failed to generate QR code"}

        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}
    
    def check_connection_status(self) -> Dict:
        """Check current connection status for Unipile WhatsApp integration"""
        status = {
            "available": bool(self.unipile_client),
            "connected": False,
            "accounts": [],
            "qr_code": self.connection_status.get("qr_code"),
            "last_check": datetime.now().isoformat()
        }

        # Check Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                whatsapp_accounts = [acc for acc in accounts.get("items", [])
                                   if acc.get("type", "").upper() == "WHATSAPP"]
                status["connected"] = len(whatsapp_accounts) > 0
                status["accounts"] = whatsapp_accounts

                # Update internal status
                self.connection_status["connected"] = status["connected"]
                self.connection_status["accounts"] = whatsapp_accounts

            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")
                status["error"] = str(e)

        self.connection_status["last_check"] = status["last_check"]
        return status
    
    def send_message(self, phone_number: str, message: str, **kwargs) -> Dict:
        """Send WhatsApp message via Unipile API"""
        # Clean phone number (remove + and spaces)
        phone_number = phone_number.replace("+", "").replace(" ", "").replace("-", "")

        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        self._rate_limit()

        try:
            result = self.unipile_client.send_whatsapp_message(phone_number, message, **kwargs)
            if "error" not in result:
                self.logger.info(f"WhatsApp message sent via Unipile to {phone_number}")
                return {
                    "success": True,
                    "result": result,
                    "method": "unipile",
                    "message_id": result.get("message_id") or result.get("id")
                }
            else:
                self.logger.error(f"Unipile failed: {result.get('error')}")
                return {"error": result.get('error')}
        except Exception as e:
            self.logger.error(f"Unipile error: {e}")
            return {"error": str(e)}
    
    def send_bulk_messages(self, recipients: List[Dict], message_template: str, delay: float = 2.0) -> List[Dict]:
        """Send WhatsApp messages to multiple recipients via Unipile"""
        if not self.unipile_client:
            return [{"error": "Unipile client not available"}]

        results = []

        for recipient in recipients:
            phone_number = recipient.get("phone_number") or recipient.get("contact")
            if not phone_number:
                continue

            # Personalize message
            try:
                personalized_message = message_template.format(**recipient)
            except KeyError:
                personalized_message = message_template

            result = self.send_message(phone_number, personalized_message)
            results.append({
                "phone_number": phone_number,
                "result": result,
                "method": result.get("method", "unipile"),
                "timestamp": datetime.now().isoformat()
            })

            if delay > 0:
                time.sleep(delay)

        return results
    
    def update_config(self, **kwargs) -> Dict:
        """Update WhatsApp configuration"""
        for key, value in kwargs.items():
            if key in ["unipile_api_key", "webhook_settings", "message_templates"]:
                self.config[key] = value

        self._save_config()
        return {"success": True, "message": "Configuration updated"}
    
    def test_connection(self) -> Dict:
        """Test Unipile WhatsApp connection"""
        if not self.unipile_client:
            return {"available": False, "connected": False, "error": "Unipile client not available"}

        try:
            accounts = self.unipile_client.get_accounts()
            whatsapp_accounts = [acc for acc in accounts.get("items", [])
                               if acc.get("type", "").upper() == "WHATSAPP"]

            return {
                "available": True,
                "connected": len(whatsapp_accounts) > 0,
                "accounts": whatsapp_accounts
            }
        except Exception as e:
            return {
                "available": True,
                "connected": False,
                "error": str(e)
            }

    def is_configured(self) -> bool:
        """Check if WhatsApp is properly configured"""
        return self.unipile_client is not None

# Example usage
if __name__ == "__main__":
    try:
        whatsapp = WhatsAppMessaging()

        # Check connection status
        status = whatsapp.check_connection_status()
        print(f"Connection status: {status}")

        # Test authentication (generates QR code if not connected)
        auth_result = whatsapp.authenticate_account()
        print(f"Authentication result: {auth_result}")

        if auth_result.get("qr_required"):
            print("Please scan the QR code with your WhatsApp mobile app")
            print("QR code data available in auth_result['qr_code']")

        # Example: Send message (uncomment to test)
        # result = whatsapp.send_message("+**********", "Hello from WhatsApp via Unipile!")
        # print(f"Message result: {result}")

        # Example: Send bulk messages (uncomment to test)
        # recipients = [
        #     {"phone_number": "+**********", "name": "John"},
        #     {"phone_number": "+**********", "name": "Jane"}
        # ]
        # bulk_result = whatsapp.send_bulk_messages(recipients, "Hello {name}! This message was sent via Unipile.")
        # print(f"Bulk result: {bulk_result}")

    except Exception as e:
        print(f"Error initializing WhatsApp messaging: {e}")
        print("Make sure Unipile API is configured properly")
