#!/usr/bin/env python3
"""
Simple WhatsApp server for testing
"""

import sys
import os
from http.server import HTT<PERSON>erver, SimpleHTTPRequestHandler
import json
import urllib.parse

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class WhatsAppHandler(SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(__file__), **kwargs)
    
    def do_POST(self):
        """Handle POST requests"""
        if self.path == '/api/whatsapp/authenticate':
            self.handle_authenticate()
        elif self.path == '/api/whatsapp/status':
            self.handle_status()
        else:
            self.send_error(404, "Not Found")
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/api/whatsapp/status':
            self.handle_status()
        elif self.path.startswith('/whatsapp/'):
            # Serve HTML files
            file_path = self.path[1:]  # Remove leading /
            if os.path.exists(file_path):
                self.serve_file(file_path)
            else:
                self.send_error(404, "File not found")
        else:
            super().do_GET()
    
    def handle_authenticate(self):
        """Handle WhatsApp authentication"""
        try:
            from whatsapp_integration.whatsapp_api import WhatsAppMessaging
            
            whatsapp = WhatsAppMessaging()
            result = whatsapp.authenticate_account()
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            self.wfile.write(json.dumps(result).encode())
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            error_response = {"success": False, "error": str(e)}
            self.wfile.write(json.dumps(error_response).encode())
    
    def handle_status(self):
        """Handle status check"""
        try:
            from whatsapp_integration.whatsapp_api import WhatsAppMessaging
            
            whatsapp = WhatsAppMessaging()
            status = whatsapp.check_connection_status()
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {"success": True, "status": status}
            self.wfile.write(json.dumps(response).encode())
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            error_response = {"success": False, "error": str(e)}
            self.wfile.write(json.dumps(error_response).encode())
    
    def serve_file(self, file_path):
        """Serve a file"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            self.send_response(200)
            
            # Set content type based on file extension
            if file_path.endswith('.html'):
                self.send_header('Content-type', 'text/html')
            elif file_path.endswith('.css'):
                self.send_header('Content-type', 'text/css')
            elif file_path.endswith('.js'):
                self.send_header('Content-type', 'application/javascript')
            else:
                self.send_header('Content-type', 'application/octet-stream')
            
            self.end_headers()
            self.wfile.write(content)
            
        except Exception as e:
            self.send_error(500, f"Error serving file: {e}")
    
    def log_message(self, format, *args):
        """Custom log message"""
        print(f"[{self.address_string()}] {format % args}")

def start_server(port=8000):
    """Start the simple WhatsApp server"""
    print(f"🚀 Starting Simple WhatsApp Server on port {port}")
    print("=" * 50)
    
    server_address = ('', port)
    httpd = HTTPServer(server_address, WhatsAppHandler)
    
    print(f"✅ Server running at: http://localhost:{port}")
    print(f"📱 WhatsApp Auth: http://localhost:{port}/whatsapp_integration/whatsapp_auth.html")
    print(f"🔗 API Endpoints:")
    print(f"   - POST /api/whatsapp/authenticate")
    print(f"   - GET /api/whatsapp/status")
    
    print("\n📊 Server is running... Press Ctrl+C to stop")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Stopping server...")
        httpd.shutdown()
        print("✅ Server stopped")

if __name__ == "__main__":
    start_server()
