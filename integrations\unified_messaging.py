"""
Unified Messaging System
Coordinates messaging across all social media platforms using Unipile API
"""

import json
import logging
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import asyncio
import concurrent.futures
from dataclasses import dataclass

# Import Unipile configuration and platform APIs
from unipile_config import get_unipile_client, is_unipile_available
from unipile_api import UnipileAPI
from whatsapp_integration.whatsapp_api import WhatsAppMessaging
from telegram_integration.telegram_api import TelegramMessaging

@dataclass
class MessageRecipient:
    """Data class for message recipients"""
    platform: str
    recipient_id: str
    name: Optional[str] = None
    metadata: Optional[Dict] = None

@dataclass
class MessageResult:
    """Data class for message results"""
    platform: str
    recipient_id: str
    success: bool
    message_id: Optional[str] = None
    error: Optional[str] = None
    timestamp: str = None

class UnifiedMessaging:
    def __init__(self, config_path: str = "unified_config.json",
                 use_unipile: bool = True):
        """Initialize unified messaging system"""
        self.config_path = config_path
        self.config = self._load_config()
        self.use_unipile = use_unipile

        # Initialize Unipile API as primary method
        if use_unipile:
            self.unipile = UnipileAPI()
        else:
            self.unipile = None

        # Initialize platform APIs as fallback
        self.platforms = {}

        # Initialize platforms safely
        try:
            self.platforms['telegram'] = TelegramMessaging()
        except Exception as e:
            self.logger.error(f"Failed to initialize Telegram: {e}")
            self.platforms['telegram'] = None

        try:
            self.platforms['whatsapp'] = WhatsAppMessaging()
        except Exception as e:
            self.logger.error(f"Failed to initialize WhatsApp: {e}")
            self.platforms['whatsapp'] = None

        # Disable other platforms for now
        self.platforms.update({
            'facebook': None,  # FacebookMessaging(),
            'instagram': None,  # InstagramMessaging(),
            'tiktok': None,  # TikTokMessaging(),
            'linkedin': None  # LinkedInMessaging()
        })

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Message queue for batch processing
        self.message_queue = []
        
    def _load_config(self) -> Dict:
        """Load unified configuration"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # Create default config
            default_config = {
                "enabled_platforms": ["whatsapp", "telegram", "facebook", "instagram", "linkedin"],
                "rate_limiting": {
                    "global_delay": 1.0,
                    "platform_delays": {
                        "whatsapp": 1.0,
                        "telegram": 0.5,
                        "facebook": 2.0,
                        "instagram": 3.0,
                        "tiktok": 2.0,
                        "linkedin": 3.0
                    }
                },
                "retry_settings": {
                    "max_retries": 3,
                    "retry_delay": 5,
                    "exponential_backoff": True
                },
                "message_templates": {
                    "welcome": "Welcome! Thanks for connecting with us.",
                    "follow_up": "Following up on your inquiry. How can we help?",
                    "promotional": "Special offer just for you! Don't miss out."
                }
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2)
            
            return default_config
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def get_platform_status(self) -> Dict[str, bool]:
        """Check configuration status of all platforms"""
        if self.use_unipile and self.unipile:
            try:
                # Use Unipile to check platform connections
                return self.unipile.get_connection_status()
            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")
                # Fall back to individual platform checks

        # Fallback to individual platform APIs
        status = {}
        for platform_name, api in self.platforms.items():
            try:
                if api is None:
                    status[platform_name] = False
                else:
                    status[platform_name] = api.is_configured()
            except Exception as e:
                self.logger.error(f"Error checking {platform_name} status: {e}")
                status[platform_name] = False

        return status

    def send_message_by_platform(self, platform: str, recipient: str, message: str, **options) -> MessageResult:
        """Send message by platform - wrapper for API compatibility"""
        return self.send_message_to_platform(platform, recipient, message, **options)

    def send_bulk_campaign(self, campaign_data: Dict[str, List[Dict]], delay: float = 2.0) -> Dict[str, List[MessageResult]]:
        """Send bulk campaign across platforms"""
        results = {}

        for platform, recipients_data in campaign_data.items():
            platform_results = []

            for recipient_data in recipients_data:
                contact = recipient_data.get("contact")
                message = recipient_data.get("message")

                if not contact or not message:
                    platform_results.append(MessageResult(
                        platform=platform,
                        recipient_id=contact or "unknown",
                        success=False,
                        error="Missing contact or message",
                        timestamp=datetime.now().isoformat()
                    ))
                    continue

                result = self.send_message_to_platform(platform, contact, message)
                platform_results.append(result)

                # Add delay between messages
                if delay > 0:
                    import time
                    time.sleep(delay)

            results[platform] = platform_results

        return results

    def get_message_analytics(self) -> Dict[str, Any]:
        """Get message analytics"""
        return {
            "total_platforms": len(self.platforms),
            "configured_platforms": len([p for p, api in self.platforms.items() if api and api.is_configured()]),
            "unipile_enabled": self.use_unipile and self.unipile is not None,
            "platform_status": self.get_platform_status()
        }
    
    def send_message_to_platform(self, platform: str, recipient_id: str,
                                message: str, **kwargs) -> MessageResult:
        """Send message to specific platform"""
        # Try Unipile first if enabled
        if self.use_unipile and self.unipile:
            try:
                result = self._send_via_unipile(platform, recipient_id, message, **kwargs)
                if result.success or "not found" not in str(result.error).lower():
                    return result
                else:
                    self.logger.warning(f"Unipile failed for {platform}, trying fallback API")
            except Exception as e:
                self.logger.warning(f"Unipile error for {platform}: {e}, trying fallback API")

        # Fallback to individual platform APIs
        return self._send_via_platform_api(platform, recipient_id, message, **kwargs)

    def _send_via_unipile(self, platform: str, recipient_id: str,
                         message: str, **kwargs) -> MessageResult:
        """Send message via Unipile API"""
        try:
            if platform == 'whatsapp':
                result = self.unipile.send_whatsapp_message(recipient_id, message)
            elif platform == 'telegram':
                result = self.unipile.send_telegram_message(recipient_id, message)
            elif platform == 'facebook':
                result = self.unipile.send_facebook_message(recipient_id, message)
            elif platform == 'instagram':
                result = self.unipile.send_instagram_message(recipient_id, message)
            elif platform == 'linkedin':
                result = self.unipile.send_linkedin_message(recipient_id, message)
            elif platform == 'tiktok':
                # TikTok not supported by Unipile for messaging
                return MessageResult(
                    platform=platform,
                    recipient_id=recipient_id,
                    success=False,
                    error="TikTok messaging not supported by Unipile",
                    timestamp=datetime.now().isoformat()
                )
            else:
                return MessageResult(
                    platform=platform,
                    recipient_id=recipient_id,
                    success=False,
                    error=f"Platform '{platform}' not supported by Unipile",
                    timestamp=datetime.now().isoformat()
                )

            # Parse Unipile result
            success = "error" not in result
            message_id = result.get("message_id") or result.get("id")
            error = result.get("error") if not success else None

            return MessageResult(
                platform=platform,
                recipient_id=recipient_id,
                success=success,
                message_id=message_id,
                error=error,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            return MessageResult(
                platform=platform,
                recipient_id=recipient_id,
                success=False,
                error=str(e),
                timestamp=datetime.now().isoformat()
            )

    def _send_via_platform_api(self, platform: str, recipient_id: str,
                              message: str, **kwargs) -> MessageResult:
        """Send message via individual platform API (fallback)"""
        if platform not in self.platforms:
            return MessageResult(
                platform=platform,
                recipient_id=recipient_id,
                success=False,
                error=f"Platform '{platform}' not supported",
                timestamp=datetime.now().isoformat()
            )

        api = self.platforms[platform]

        if api is None:
            return MessageResult(
                platform=platform,
                recipient_id=recipient_id,
                success=False,
                error=f"Platform '{platform}' API not implemented yet",
                timestamp=datetime.now().isoformat()
            )

        if not api.is_configured():
            return MessageResult(
                platform=platform,
                recipient_id=recipient_id,
                success=False,
                error=f"Platform '{platform}' not configured",
                timestamp=datetime.now().isoformat()
            )

        try:
            # Platform-specific message sending
            if platform == 'whatsapp':
                result = api.send_message(recipient_id, message)
            elif platform == 'telegram':
                result = api.send_message(recipient_id, message)
            elif platform == 'facebook':
                result = api.send_text_message(recipient_id, message)
            elif platform == 'instagram':
                result = api.send_direct_message(recipient_id, message)
            elif platform == 'tiktok':
                # TikTok doesn't have direct messaging, use engagement message
                result = api.send_engagement_message(recipient_id, "follow_up")
            elif platform == 'linkedin':
                subject = kwargs.get('subject', 'Message from LinkedIn')
                result = api.send_message(recipient_id, subject, message)
            else:
                raise ValueError(f"Unsupported platform: {platform}")

            # Parse result
            success = "error" not in result and result.get("ok", True)
            message_id = result.get("message_id") or result.get("id")
            error = result.get("error") if not success else None

            return MessageResult(
                platform=platform,
                recipient_id=recipient_id,
                success=success,
                message_id=message_id,
                error=error,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            self.logger.error(f"Error sending message via {platform}: {e}")
            return MessageResult(
                platform=platform,
                recipient_id=recipient_id,
                success=False,
                error=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def send_bulk_messages(self, recipients: List[MessageRecipient], 
                          message: str, **kwargs) -> List[MessageResult]:
        """Send message to multiple recipients across platforms"""
        results = []
        platform_delays = self.config.get("rate_limiting", {}).get("platform_delays", {})
        
        for recipient in recipients:
            # Send message
            result = self.send_message_to_platform(
                recipient.platform, 
                recipient.recipient_id, 
                message, 
                **kwargs
            )
            results.append(result)
            
            # Apply rate limiting delay
            delay = platform_delays.get(recipient.platform, 1.0)
            if delay > 0:
                import time
                time.sleep(delay)
        
        return results
    
    def send_template_message(self, recipients: List[MessageRecipient], 
                            template_name: str, **template_vars) -> List[MessageResult]:
        """Send template message to multiple recipients"""
        templates = self.config.get("message_templates", {})
        
        if template_name not in templates:
            error_result = MessageResult(
                platform="unified",
                recipient_id="all",
                success=False,
                error=f"Template '{template_name}' not found",
                timestamp=datetime.now().isoformat()
            )
            return [error_result]
        
        message = templates[template_name].format(**template_vars)
        return self.send_bulk_messages(recipients, message)
    
    def send_personalized_messages(self, recipient_data: List[Dict]) -> List[MessageResult]:
        """Send personalized messages to recipients"""
        results = []
        
        for data in recipient_data:
            platform = data.get("platform")
            recipient_id = data.get("recipient_id")
            message = data.get("message")
            
            if not all([platform, recipient_id, message]):
                results.append(MessageResult(
                    platform=platform or "unknown",
                    recipient_id=recipient_id or "unknown",
                    success=False,
                    error="Missing required fields: platform, recipient_id, or message",
                    timestamp=datetime.now().isoformat()
                ))
                continue
            
            result = self.send_message_to_platform(platform, recipient_id, message)
            results.append(result)
        
        return results
    
    async def send_messages_async(self, recipients: List[MessageRecipient], 
                                 message: str, **kwargs) -> List[MessageResult]:
        """Send messages asynchronously for better performance"""
        loop = asyncio.get_event_loop()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            tasks = []
            
            for recipient in recipients:
                task = loop.run_in_executor(
                    executor,
                    self.send_message_to_platform,
                    recipient.platform,
                    recipient.recipient_id,
                    message
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append(MessageResult(
                        platform=recipients[i].platform,
                        recipient_id=recipients[i].recipient_id,
                        success=False,
                        error=str(result),
                        timestamp=datetime.now().isoformat()
                    ))
                else:
                    processed_results.append(result)
            
            return processed_results
    
    def get_platform_analytics(self) -> Dict[str, Dict]:
        """Get analytics from all configured platforms"""
        analytics = {}
        
        for platform_name, api in self.platforms.items():
            if not api.is_configured():
                continue
            
            try:
                if platform_name == 'whatsapp':
                    # WhatsApp doesn't have built-in analytics API
                    analytics[platform_name] = {"status": "configured", "analytics": "not_available"}
                elif platform_name == 'telegram':
                    # Get bot info as basic analytics
                    info = api.get_me()
                    analytics[platform_name] = {"bot_info": info}
                elif platform_name == 'facebook':
                    # Facebook page analytics would require additional API calls
                    analytics[platform_name] = {"status": "configured", "analytics": "requires_page_insights_api"}
                elif platform_name == 'instagram':
                    # Get account info
                    info = api.get_account_info()
                    analytics[platform_name] = {"account_info": info}
                elif platform_name == 'tiktok':
                    # Get user info
                    info = api.get_user_info()
                    analytics[platform_name] = {"user_info": info}
                elif platform_name == 'linkedin':
                    # Get profile info
                    info = api.get_profile()
                    analytics[platform_name] = {"profile_info": info}
                
            except Exception as e:
                analytics[platform_name] = {"error": str(e)}
        
        return analytics
    
    def export_message_results(self, results: List[MessageResult], 
                              filename: str = None) -> str:
        """Export message results to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"message_results_{timestamp}.json"
        
        # Convert results to dictionaries
        results_data = []
        for result in results:
            results_data.append({
                "platform": result.platform,
                "recipient_id": result.recipient_id,
                "success": result.success,
                "message_id": result.message_id,
                "error": result.error,
                "timestamp": result.timestamp
            })
        
        # Add summary
        export_data = {
            "summary": {
                "total_messages": len(results),
                "successful": sum(1 for r in results if r.success),
                "failed": sum(1 for r in results if not r.success),
                "platforms": list(set(r.platform for r in results)),
                "export_timestamp": datetime.now().isoformat()
            },
            "results": results_data
        }
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        self.logger.info(f"Message results exported to {filename}")
        return filename
    
    def get_configuration_guide(self) -> Dict[str, str]:
        """Get configuration guide for all platforms"""
        return {
            "whatsapp": "Configure WhatsApp Business API with phone_number_id and access_token",
            "telegram": "Create a bot via @BotFather and get bot_token",
            "facebook": "Create Facebook App and get page_access_token",
            "instagram": "Connect Instagram Business account to Facebook Page",
            "tiktok": "Apply for TikTok Developer access and get client credentials",
            "linkedin": "Create LinkedIn App and complete OAuth flow for access_token"
        }

# Example usage
if __name__ == "__main__":
    # Initialize unified messaging
    unified = UnifiedMessaging()
    
    # Check platform status
    status = unified.get_platform_status()
    print("Platform Status:", status)
    
    # Example: Send message to multiple platforms
    recipients = [
        MessageRecipient(platform="telegram", recipient_id="@username", name="Test User"),
        MessageRecipient(platform="whatsapp", recipient_id="+**********", name="WhatsApp User")
    ]
    
    # Send bulk messages (uncomment to test)
    # results = unified.send_bulk_messages(recipients, "Hello from unified messaging!")
    # print("Results:", results)
