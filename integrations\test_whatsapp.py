#!/usr/bin/env python3
"""
Test WhatsApp integration with Unipile API
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_unipile_api():
    """Test basic Unipile API connection"""
    print("🔗 Testing Unipile API Connection")
    print("=" * 40)
    
    try:
        from unipile_api import UnipileAPI
        
        client = UnipileAPI()
        accounts = client.get_accounts()
        
        if "error" not in accounts:
            print("✅ Unipile API connection successful!")
            print(f"📊 Total accounts: {len(accounts.get('items', []))}")
            
            # Check for WhatsApp accounts
            whatsapp_accounts = [acc for acc in accounts.get('items', []) if acc.get('type') == 'WHATSAPP']
            print(f"📱 WhatsApp accounts: {len(whatsapp_accounts)}")
            
            if whatsapp_accounts:
                for acc in whatsapp_accounts:
                    print(f"   - Account ID: {acc.get('id')}")
                    print(f"   - Status: {acc.get('status')}")
            
            return True, accounts
        else:
            print(f"❌ Unipile API error: {accounts}")
            return False, accounts
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None

def test_whatsapp_initialization():
    """Test WhatsApp messaging initialization"""
    print("\n📱 Testing WhatsApp Initialization")
    print("=" * 40)
    
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        
        whatsapp = WhatsAppMessaging()
        
        if whatsapp.unipile_client:
            print("✅ WhatsApp messaging initialized successfully!")
            print(f"📡 Unipile client available: {whatsapp.unipile_client is not None}")
            return True, whatsapp
        else:
            print("❌ WhatsApp initialization failed - no Unipile client")
            return False, None
            
    except Exception as e:
        print(f"❌ WhatsApp initialization error: {e}")
        return False, None

def test_whatsapp_status():
    """Test WhatsApp connection status"""
    print("\n📊 Testing WhatsApp Status")
    print("=" * 40)
    
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        
        whatsapp = WhatsAppMessaging()
        status = whatsapp.check_connection_status()
        
        print(f"📡 Available: {status.get('available')}")
        print(f"🔗 Connected: {status.get('connected')}")
        print(f"📱 Accounts: {len(status.get('accounts', []))}")
        
        if status.get('connected'):
            print("✅ WhatsApp is connected and ready!")
            return True, status
        else:
            print("⚠️  WhatsApp not connected - authentication needed")
            return False, status
            
    except Exception as e:
        print(f"❌ Status check error: {e}")
        return False, None

def test_qr_generation():
    """Test QR code generation for WhatsApp authentication"""
    print("\n🔲 Testing QR Code Generation")
    print("=" * 40)
    
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        
        whatsapp = WhatsAppMessaging()
        auth_result = whatsapp.authenticate_account()
        
        if auth_result.get("success"):
            if auth_result.get("qr_required"):
                print("✅ QR code generated successfully!")
                print("📱 Please scan the QR code with your WhatsApp mobile app")
                print(f"🔲 QR code available: {auth_result.get('qr_code') is not None}")
                return True, auth_result
            else:
                print("✅ WhatsApp account already connected!")
                return True, auth_result
        else:
            print(f"❌ QR generation failed: {auth_result.get('error')}")
            return False, auth_result
            
    except Exception as e:
        print(f"❌ QR generation error: {e}")
        return False, None

def show_next_steps():
    """Show next steps for WhatsApp setup"""
    print("\n🚀 Next Steps for WhatsApp Setup")
    print("=" * 50)
    
    print("1. 🌐 Start your API server:")
    print("   cd integrations/api")
    print("   python api_endpoints.py")
    
    print("\n2. 📱 Open WhatsApp authentication page:")
    print("   http://localhost:8000/whatsapp/whatsapp_auth.html")
    
    print("\n3. 🔲 Generate QR code and scan with your phone:")
    print("   - Click 'Generate QR Code' button")
    print("   - Open WhatsApp on your phone")
    print("   - Go to Settings > Linked Devices")
    print("   - Tap 'Link a Device' and scan the QR code")
    
    print("\n4. ✅ Test messaging:")
    print("   - Use the test form on the auth page")
    print("   - Or use the API endpoint: POST /api/messaging/send")
    
    print("\n5. 📊 Check status:")
    print("   - GET /api/whatsapp/status")
    print("   - GET /api/messaging/status/whatsapp")

if __name__ == "__main__":
    print("🚀 WhatsApp Integration Test")
    print("=" * 50)
    
    # Run tests
    tests = [
        ("Unipile API Connection", test_unipile_api),
        ("WhatsApp Initialization", test_whatsapp_initialization),
        ("WhatsApp Status", test_whatsapp_status),
        ("QR Code Generation", test_qr_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success, data = test_func()
        results.append((test_name, success))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! WhatsApp integration is ready.")
    else:
        print("\n⚠️  Some tests failed, but basic setup should work.")
    
    show_next_steps()
