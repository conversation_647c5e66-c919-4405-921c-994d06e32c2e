#!/usr/bin/env python3
"""
Simple WhatsApp test
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🚀 Testing WhatsApp Integration")
print("=" * 40)

try:
    from whatsapp_integration.whatsapp_api import WhatsAppMessaging
    
    print("✅ WhatsApp module imported successfully")
    
    whatsapp = WhatsAppMessaging()
    print("✅ WhatsApp instance created")
    
    # Test authentication (QR generation)
    print("\n🔲 Testing QR Code Generation...")
    auth_result = whatsapp.authenticate_account()
    
    if auth_result.get("success"):
        print("✅ QR Code generated successfully!")
        print(f"   QR Required: {auth_result.get('qr_required')}")
        print(f"   Account ID: {auth_result.get('account_id')}")
        print(f"   Message: {auth_result.get('message')}")
        
        if auth_result.get("qr_code"):
            print("   📱 QR Code image ready for display")
        
    else:
        print(f"❌ QR Generation failed: {auth_result.get('error')}")
    
    # Test connection status
    print("\n📊 Testing Connection Status...")
    status = whatsapp.check_connection_status()
    print(f"   Available: {status.get('available')}")
    print(f"   Connected: {status.get('connected')}")
    print(f"   Accounts: {len(status.get('accounts', []))}")
    
    print("\n🎉 WhatsApp integration test completed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
