#!/usr/bin/env python3
"""
Test script to verify the new Unipile API configuration
"""

import requests
import json
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_new_unipile_api():
    """Test the new Unipile API endpoint and key"""
    print("🚀 Testing New Unipile API Configuration")
    print("=" * 50)
    
    # New API configuration
    api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
    base_url = "https://api8.unipile.com:13814/api/v1"
    
    print(f"📡 API Endpoint: {base_url}")
    print(f"🔑 API Key: {api_key[:20]}...")
    
    # Test 1: Direct API call to get accounts
    print("\n1️⃣ Testing Direct API Call")
    try:
        headers = {
            "X-API-KEY": api_key,
            "accept": "application/json"
        }
        
        response = requests.get(f"{base_url}/accounts", headers=headers, timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ API connection successful!")
            print(f"   📊 Response: {json.dumps(data, indent=2)[:200]}...")
        else:
            print(f"   ❌ API call failed: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Using UnipileAPI class
    print("\n2️⃣ Testing UnipileAPI Class")
    try:
        from unipile_api import UnipileAPI
        
        client = UnipileAPI()  # Should use new defaults
        accounts = client.get_accounts()
        
        if "error" not in accounts:
            print("   ✅ UnipileAPI class working!")
            print(f"   📊 Accounts found: {len(accounts.get('items', []))}")
        else:
            print(f"   ❌ UnipileAPI error: {accounts}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Configuration files
    print("\n3️⃣ Testing Configuration Files")
    
    config_files = [
        "unipile_config.json",
        "unified_config.json",
        "linkedin_integration/config.json",
        "whatsapp_integration/config.json",
        "telegram_integration/config.json",
        "instagram_integration/config.json",
        "tiktok_integration/config.json"
    ]
    
    for config_file in config_files:
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Check for new API endpoint
            config_str = json.dumps(config)
            if "api8.unipile.com:13814" in config_str:
                print(f"   ✅ {config_file}: Updated to new endpoint")
            elif "api1.unipile.com:13115" in config_str:
                print(f"   ❌ {config_file}: Still has old endpoint")
            else:
                print(f"   ℹ️  {config_file}: No Unipile endpoint found")
                
        except Exception as e:
            print(f"   ❌ {config_file}: Error reading - {e}")

def test_curl_command():
    """Test the exact curl command provided by user"""
    print("\n4️⃣ Testing Provided cURL Command")
    
    import subprocess
    
    curl_command = [
        "curl",
        "--request", "GET",
        "--url", "https://api8.unipile.com:13814/api/v1/accounts",
        "--header", "X-API-KEY:K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk=",
        "--header", "accept: application/json"
    ]
    
    try:
        result = subprocess.run(curl_command, capture_output=True, text=True, timeout=10)
        
        print(f"   Exit Code: {result.returncode}")
        print(f"   Response: {result.stdout[:200]}...")
        
        if result.returncode == 0:
            print("   ✅ cURL command successful!")
        else:
            print(f"   ❌ cURL error: {result.stderr}")
            
    except Exception as e:
        print(f"   ❌ Error running cURL: {e}")

if __name__ == "__main__":
    test_new_unipile_api()
    test_curl_command()
    
    print("\n" + "=" * 50)
    print("🎯 Summary:")
    print("- New API endpoint: https://api8.unipile.com:13814/api/v1")
    print("- New API key: K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk=")
    print("- All configuration files should be updated")
    print("- Test the integration by running this script")
