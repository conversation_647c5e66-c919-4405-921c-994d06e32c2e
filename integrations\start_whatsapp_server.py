#!/usr/bin/env python3
"""
Simple server starter for WhatsApp integration
"""

import sys
import os
import subprocess
import time

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_server():
    """Start the WhatsApp API server"""
    print("🚀 Starting WhatsApp API Server")
    print("=" * 40)
    
    # Change to API directory
    api_dir = os.path.join(os.path.dirname(__file__), "api")
    
    print(f"📁 API Directory: {api_dir}")
    
    # Try different methods to start the server
    methods = [
        ["python", "api_endpoints.py"],
        ["python", "-m", "uvicorn", "api_endpoints:app", "--host", "0.0.0.0", "--port", "8000"],
        ["uvicorn", "api_endpoints:app", "--host", "0.0.0.0", "--port", "8000"]
    ]
    
    for i, method in enumerate(methods, 1):
        print(f"\n{i}️⃣ Trying method {i}: {' '.join(method)}")
        
        try:
            # Start the server process
            process = subprocess.Popen(
                method,
                cwd=api_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a moment to see if it starts successfully
            time.sleep(3)
            
            # Check if process is still running
            if process.poll() is None:
                print(f"✅ Server started successfully with method {i}!")
                print(f"🌐 Server running at: http://localhost:8000")
                print(f"📱 WhatsApp Auth: http://localhost:8000/whatsapp/whatsapp_auth.html")
                
                # Keep the server running
                try:
                    print("\n📊 Server is running... Press Ctrl+C to stop")
                    process.wait()
                except KeyboardInterrupt:
                    print("\n🛑 Stopping server...")
                    process.terminate()
                    process.wait()
                    print("✅ Server stopped")
                
                return True
            else:
                # Process ended, check for errors
                stdout, stderr = process.communicate()
                print(f"❌ Method {i} failed:")
                if stderr:
                    print(f"   Error: {stderr[:200]}...")
                if stdout:
                    print(f"   Output: {stdout[:200]}...")
                    
        except Exception as e:
            print(f"❌ Method {i} exception: {e}")
    
    print("\n❌ All methods failed to start the server")
    return False

def show_manual_instructions():
    """Show manual instructions if automatic start fails"""
    print("\n📋 MANUAL SERVER START INSTRUCTIONS:")
    print("=" * 50)
    
    print("\n1️⃣ Open a new terminal/command prompt")
    print("2️⃣ Navigate to the integrations/api directory:")
    print("   cd integrations/api")
    
    print("\n3️⃣ Try one of these commands:")
    print("   python api_endpoints.py")
    print("   OR")
    print("   python -m uvicorn api_endpoints:app --host 0.0.0.0 --port 8000")
    
    print("\n4️⃣ Once server starts, open in browser:")
    print("   http://localhost:8000/whatsapp/whatsapp_auth.html")
    
    print("\n5️⃣ Generate QR code and scan with your phone")

if __name__ == "__main__":
    success = start_server()
    
    if not success:
        show_manual_instructions()
    
    print("\n" + "=" * 50)
    print("📱 WhatsApp Integration Summary:")
    print("✅ Unipile API: Connected")
    print("✅ QR Code Generation: Working")
    print("✅ WhatsApp Integration: Ready")
    print("🔗 Next: Connect your WhatsApp by scanning QR code")
