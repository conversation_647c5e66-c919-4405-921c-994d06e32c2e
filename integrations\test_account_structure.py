#!/usr/bin/env python3
"""
Analyze the existing account structure to understand how to add WhatsApp
"""

import requests
import json

def analyze_existing_accounts():
    """Analyze the structure of existing accounts"""
    print("🔍 Analyzing Existing Account Structure")
    print("=" * 50)
    
    api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
    base_url = "https://api8.unipile.com:13814/api/v1"
    
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json"
    }
    
    try:
        response = requests.get(f"{base_url}/accounts", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Successfully retrieved accounts")
            print(f"📊 Full response structure:")
            print(json.dumps(data, indent=2))
            
            # Analyze each account
            accounts = data.get("items", [])
            print(f"\n📱 Found {len(accounts)} account(s):")
            
            for i, account in enumerate(accounts):
                print(f"\n--- Account {i+1} ---")
                print(f"ID: {account.get('id')}")
                print(f"Type: {account.get('type')}")
                print(f"Status: {account.get('status')}")
                print(f"Provider: {account.get('provider')}")
                print(f"Object: {account.get('object')}")
                
                # Check connection params
                conn_params = account.get('connection_params', {})
                if conn_params:
                    print(f"Connection Params: {list(conn_params.keys())}")
                
                # Check for any auth-related fields
                auth_fields = ['auth_url', 'qr_code', 'auth_token', 'access_token']
                for field in auth_fields:
                    if field in account:
                        print(f"{field}: {account[field]}")
            
            return data
        else:
            print(f"❌ Failed to get accounts: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_account_creation_methods():
    """Test different methods for creating/adding accounts"""
    print("\n🔧 Testing Account Creation Methods")
    print("=" * 50)
    
    api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
    base_url = "https://api8.unipile.com:13814/api/v1"
    
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Different payload structures to try
    payloads = [
        {"type": "WHATSAPP"},
        {"provider": "WHATSAPP"},
        {"platform": "WHATSAPP"},
        {"service": "WHATSAPP"},
        {"account_type": "WHATSAPP"},
        {"provider": "whatsapp"},
        {"type": "whatsapp"},
    ]
    
    # Different endpoints to try
    endpoints = [
        "accounts",
        "account",
        "connect",
        "auth/connect",
        "oauth/connect"
    ]
    
    for endpoint in endpoints:
        for payload in payloads:
            print(f"\n🔗 Testing POST /{endpoint} with {payload}")
            try:
                url = f"{base_url}/{endpoint}"
                response = requests.post(url, headers=headers, json=payload, timeout=10)
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code in [200, 201]:
                    try:
                        result = response.json()
                        print(f"   ✅ Success: {json.dumps(result, indent=2)[:300]}...")
                        return endpoint, payload, result
                    except:
                        print(f"   ✅ Success: {response.text[:200]}...")
                        return endpoint, payload, response.text
                elif response.status_code == 400:
                    try:
                        error = response.json()
                        print(f"   ⚠️  Bad Request: {error.get('detail', error)}")
                    except:
                        print(f"   ⚠️  Bad Request: {response.text[:100]}...")
                elif response.status_code == 404:
                    print(f"   ❌ Not Found")
                else:
                    try:
                        error = response.json()
                        print(f"   ⚠️  Error {response.status_code}: {error.get('detail', error)}")
                    except:
                        print(f"   ⚠️  Error {response.status_code}: {response.text[:100]}...")
                        
            except Exception as e:
                print(f"   ❌ Exception: {e}")
    
    return None, None, None

def check_unipile_dashboard():
    """Check if we can access Unipile dashboard info"""
    print("\n🌐 Checking Unipile Dashboard Access")
    print("=" * 50)
    
    print("💡 Based on the existing LinkedIn account, it seems like:")
    print("   - Accounts are added through the Unipile dashboard")
    print("   - The API is for managing existing connected accounts")
    print("   - WhatsApp might need to be added via web interface first")
    
    print("\n🔗 Try accessing Unipile dashboard:")
    print("   - Go to https://dashboard.unipile.com")
    print("   - Login with your account")
    print("   - Look for 'Add Account' or 'Connect WhatsApp' option")
    print("   - Follow the WhatsApp connection process there")

if __name__ == "__main__":
    # Analyze existing accounts
    accounts_data = analyze_existing_accounts()
    
    # Test account creation methods
    success_endpoint, success_payload, success_result = test_account_creation_methods()
    
    # Show dashboard info
    check_unipile_dashboard()
    
    print("\n" + "=" * 50)
    print("📋 Analysis Summary:")
    
    if accounts_data:
        accounts = accounts_data.get("items", [])
        print(f"✅ Found {len(accounts)} existing account(s)")
        for account in accounts:
            print(f"   - {account.get('type', 'Unknown')} account (ID: {account.get('id')})")
    
    if success_endpoint:
        print(f"✅ Found working endpoint: POST /{success_endpoint}")
        print(f"   Payload: {success_payload}")
    else:
        print("❌ No working API endpoint found for adding accounts")
        print("💡 Accounts likely need to be added via Unipile dashboard")
    
    print("\n🚀 Next Steps:")
    print("1. Check Unipile dashboard for WhatsApp connection")
    print("2. If dashboard method works, update our integration")
    print("3. Focus on messaging with existing connected accounts")
