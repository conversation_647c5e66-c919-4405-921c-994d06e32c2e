#!/usr/bin/env python3
"""
Test Unipile API endpoints to find the correct one for WhatsApp connection
"""

import requests
import json

def test_unipile_endpoints():
    """Test various Unipile API endpoints"""
    print("🔍 Testing Unipile API Endpoints")
    print("=" * 50)
    
    api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
    base_url = "https://api8.unipile.com:13814/api/v1"
    
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Test different endpoints
    endpoints_to_test = [
        ("GET", "accounts", None),
        ("POST", "accounts/connect", {"provider": "WHATSAPP"}),
        ("POST", "accounts/add", {"provider": "WHATSAPP"}),
        ("POST", "connect", {"provider": "WHATSAPP"}),
        ("POST", "auth", {"provider": "WHATSAPP"}),
        ("POST", "authenticate", {"provider": "WHATSAPP"}),
    ]
    
    for method, endpoint, data in endpoints_to_test:
        print(f"\n🔗 Testing {method} /{endpoint}")
        try:
            url = f"{base_url}/{endpoint}"
            
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            else:
                response = requests.post(url, headers=headers, json=data, timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   ✅ Success: {json.dumps(result, indent=2)[:200]}...")
                except:
                    print(f"   ✅ Success: {response.text[:200]}...")
            elif response.status_code == 404:
                print(f"   ❌ Not Found: {endpoint} doesn't exist")
            else:
                try:
                    error = response.json()
                    print(f"   ⚠️  Error: {error}")
                except:
                    print(f"   ⚠️  Error: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_whatsapp_specific():
    """Test WhatsApp-specific endpoints"""
    print("\n📱 Testing WhatsApp-Specific Endpoints")
    print("=" * 50)
    
    api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
    base_url = "https://api8.unipile.com:13814/api/v1"
    
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Test WhatsApp-specific endpoints
    whatsapp_endpoints = [
        ("GET", "whatsapp", None),
        ("POST", "whatsapp/connect", None),
        ("POST", "whatsapp/auth", None),
        ("GET", "providers", None),
        ("GET", "providers/whatsapp", None),
    ]
    
    for method, endpoint, data in whatsapp_endpoints:
        print(f"\n🔗 Testing {method} /{endpoint}")
        try:
            url = f"{base_url}/{endpoint}"
            
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            else:
                response = requests.post(url, headers=headers, json=data, timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   ✅ Success: {json.dumps(result, indent=2)[:200]}...")
                except:
                    print(f"   ✅ Success: {response.text[:200]}...")
            elif response.status_code == 404:
                print(f"   ❌ Not Found: {endpoint} doesn't exist")
            else:
                try:
                    error = response.json()
                    print(f"   ⚠️  Error: {error.get('detail', error)}")
                except:
                    print(f"   ⚠️  Error: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def check_api_documentation():
    """Check if there's API documentation endpoint"""
    print("\n📚 Checking for API Documentation")
    print("=" * 50)
    
    api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
    base_url = "https://api8.unipile.com:13814"
    
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json"
    }
    
    doc_endpoints = [
        "/docs",
        "/api/v1/docs",
        "/swagger",
        "/api/v1/swagger",
        "/openapi.json",
        "/api/v1/openapi.json"
    ]
    
    for endpoint in doc_endpoints:
        print(f"\n🔗 Testing {endpoint}")
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, headers=headers, timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ Found documentation at: {url}")
                return url
            else:
                print(f"   ❌ Not found")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    return None

if __name__ == "__main__":
    test_unipile_endpoints()
    test_whatsapp_specific()
    doc_url = check_api_documentation()
    
    print("\n" + "=" * 50)
    print("📋 Summary:")
    print("- Test different endpoints to find the correct WhatsApp connection method")
    print("- Look for successful responses that might contain QR codes")
    if doc_url:
        print(f"- Check API documentation at: {doc_url}")
    else:
        print("- No API documentation found at common endpoints")
    
    print("\n💡 Next Steps:")
    print("1. Check which endpoint returned success for WhatsApp connection")
    print("2. Look for QR code data in successful responses")
    print("3. Update the WhatsApp integration with the correct endpoint")
