#!/usr/bin/env python3
"""
Simple check of Unipile accounts
"""

import requests
import json

api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
base_url = "https://api8.unipile.com:13814/api/v1"

headers = {
    "X-API-KEY": api_key,
    "accept": "application/json"
}

print("🔍 Checking Unipile Accounts")
print("=" * 30)

try:
    response = requests.get(f"{base_url}/accounts", headers=headers, timeout=10)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Success!")
        print(json.dumps(data, indent=2))
    else:
        print(f"❌ Error {response.status_code}: {response.text}")
        
except Exception as e:
    print(f"❌ Exception: {e}")

print("\n🔧 Testing WhatsApp account creation")
print("=" * 30)

# Try the most likely endpoint for account creation
try:
    payload = {"provider": "WHATSAPP"}
    response = requests.post(f"{base_url}/accounts", headers={**headers, "Content-Type": "application/json"}, json=payload, timeout=10)
    
    print(f"Status: {response.status_code}")
    if response.status_code in [200, 201]:
        print("✅ Success!")
        print(json.dumps(response.json(), indent=2))
    else:
        print(f"Response: {response.text}")
        
except Exception as e:
    print(f"❌ Exception: {e}")
