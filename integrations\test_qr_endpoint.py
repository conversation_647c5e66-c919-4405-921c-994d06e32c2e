#!/usr/bin/env python3
"""
Test the QR code endpoint directly
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_qr_generation():
    """Test QR code generation directly"""
    print("🔲 Testing QR Code Generation")
    print("=" * 40)
    
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        
        whatsapp = WhatsAppMessaging()
        print("✅ WhatsApp instance created")
        
        # Test authentication (QR generation)
        auth_result = whatsapp.authenticate_account()
        
        print(f"📊 Auth Result Keys: {list(auth_result.keys())}")
        
        if auth_result.get("success"):
            print("✅ QR Code generated successfully!")
            print(f"   QR Required: {auth_result.get('qr_required')}")
            print(f"   Account ID: {auth_result.get('account_id')}")
            print(f"   Message: {auth_result.get('message')}")
            
            qr_code = auth_result.get("qr_code")
            if qr_code:
                print(f"   📱 QR Code: {qr_code[:50]}...")
                print(f"   📏 QR Code Length: {len(qr_code)}")
                
                # Check if it's a proper base64 image
                if qr_code.startswith("data:image/"):
                    print("   ✅ QR Code is properly formatted as base64 image")
                else:
                    print("   ❌ QR Code is not in expected base64 image format")
            else:
                print("   ❌ No QR code in response")
                
        else:
            print(f"❌ QR Generation failed: {auth_result.get('error')}")
            
        return auth_result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_api_endpoint():
    """Test the API endpoint directly"""
    print("\n🌐 Testing API Endpoint")
    print("=" * 40)
    
    try:
        import requests
        
        # Test the authenticate endpoint
        response = requests.post(
            'http://localhost:8000/api/whatsapp/authenticate',
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📡 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 Response Keys: {list(data.keys())}")
            
            if data.get("success") and data.get("qr_code"):
                qr_code = data.get("qr_code")
                print(f"✅ API returned QR code: {qr_code[:50]}...")
                
                if qr_code.startswith("data:image/"):
                    print("   ✅ QR Code is properly formatted")
                else:
                    print("   ❌ QR Code format issue")
            else:
                print(f"❌ API response issue: {data}")
        else:
            print(f"❌ API error: {response.text}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

if __name__ == "__main__":
    # Test direct function call
    result = test_qr_generation()
    
    # Test API endpoint
    test_api_endpoint()
    
    print("\n" + "=" * 40)
    print("🎯 Summary:")
    if result and result.get("success") and result.get("qr_code"):
        print("✅ QR code generation is working!")
        print("✅ The issue might be in the HTML/JavaScript display")
    else:
        print("❌ QR code generation has issues")
        print("🔧 Need to debug the WhatsApp integration")
