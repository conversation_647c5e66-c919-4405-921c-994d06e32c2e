#!/usr/bin/env python3
"""
Simple test to verify error suppression
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_telegram_init():
    """Test Telegram initialization without errors"""
    print("🤖 Testing Telegram Initialization")
    try:
        from telegram_integration.telegram_api import TelegramMessaging
        telegram = TelegramMessaging()
        print(f"✅ Telegram initialized successfully, status: {telegram.connection_status}")
        return True
    except Exception as e:
        print(f"❌ Telegram error: {e}")
        return False

def test_whatsapp_init():
    """Test WhatsApp initialization"""
    print("\n📱 Testing WhatsApp Initialization")
    try:
        from whatsapp_integration.whatsapp_api import WhatsAppMessaging
        whatsapp = WhatsAppMessaging()
        print("✅ WhatsApp initialized successfully")
        return True
    except Exception as e:
        print(f"❌ WhatsApp error: {e}")
        return False

def test_unified_messaging():
    """Test UnifiedMessaging initialization"""
    print("\n🔄 Testing UnifiedMessaging Initialization")
    try:
        from unified_messaging import UnifiedMessaging
        unified = UnifiedMessaging()
        print("✅ UnifiedMessaging initialized successfully")
        return True
    except Exception as e:
        print(f"❌ UnifiedMessaging error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Simple Error Suppression Test")
    print("=" * 40)
    
    results = []
    results.append(("Telegram", test_telegram_init()))
    results.append(("WhatsApp", test_whatsapp_init()))
    results.append(("UnifiedMessaging", test_unified_messaging()))
    
    print("\n" + "=" * 40)
    print("📊 Results:")
    all_passed = True
    for name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️  Some tests failed.")
